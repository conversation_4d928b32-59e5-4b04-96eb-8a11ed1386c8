﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E1FED4EF-94B8-311F-997C-B8FE5C1D2596}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>novel_app</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\project\vs code\novel_app002\novel_app\build\windows\x64\runner\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">novel_app.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">novel_app</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\project\vs code\novel_app002\novel_app\build\windows\x64\runner\Profile\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">novel_app.dir\Profile\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">novel_app</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\project\vs code\novel_app002\novel_app\build\windows\x64\runner\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">novel_app.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">novel_app</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\project\vs code\novel_app002\novel_app\windows;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\just_audio_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_HAS_EXCEPTIONS=0;_DEBUG;FLUTTER_VERSION="4.2.9";FLUTTER_VERSION_MAJOR=4;FLUTTER_VERSION_MINOR=2;FLUTTER_VERSION_PATCH=9;FLUTTER_VERSION_BUILD=0;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"4.2.9\";FLUTTER_VERSION_MAJOR=4;FLUTTER_VERSION_MINOR=2;FLUTTER_VERSION_PATCH=9;FLUTTER_VERSION_BUILD=0;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\project\vs code\novel_app002\novel_app\windows;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\just_audio_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\project\vs code\novel_app002\novel_app\windows;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\just_audio_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Debug\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\file_selector_windows\Debug\file_selector_windows_plugin.lib;..\plugins\just_audio_windows\Debug\just_audio_windows_plugin.lib;..\plugins\permission_handler_windows\Debug\permission_handler_windows_plugin.lib;..\plugins\share_plus\Debug\share_plus_plugin.lib;..\plugins\url_launcher_windows\Debug\url_launcher_windows_plugin.lib;..\flutter\Debug\flutter_wrapper_app.lib;winmm.lib;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/project/vs code/novel_app002/novel_app/build/windows/x64/runner/Debug/novel_app.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/project/vs code/novel_app002/novel_app/build/windows/x64/runner/Debug/novel_app.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\project\vs code\novel_app002\novel_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\project\vs code\novel_app002\novel_app\windows;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\just_audio_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="4.2.9";FLUTTER_VERSION_MAJOR=4;FLUTTER_VERSION_MINOR=2;FLUTTER_VERSION_PATCH=9;FLUTTER_VERSION_BUILD=0;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Profile"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"4.2.9\";FLUTTER_VERSION_MAJOR=4;FLUTTER_VERSION_MINOR=2;FLUTTER_VERSION_PATCH=9;FLUTTER_VERSION_BUILD=0;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Profile\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\project\vs code\novel_app002\novel_app\windows;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\just_audio_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\project\vs code\novel_app002\novel_app\windows;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\just_audio_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Profile\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\file_selector_windows\Profile\file_selector_windows_plugin.lib;..\plugins\just_audio_windows\Profile\just_audio_windows_plugin.lib;..\plugins\permission_handler_windows\Profile\permission_handler_windows_plugin.lib;..\plugins\share_plus\Profile\share_plus_plugin.lib;..\plugins\url_launcher_windows\Profile\url_launcher_windows_plugin.lib;..\flutter\Profile\flutter_wrapper_app.lib;winmm.lib;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/project/vs code/novel_app002/novel_app/build/windows/x64/runner/Profile/novel_app.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/project/vs code/novel_app002/novel_app/build/windows/x64/runner/Profile/novel_app.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\project\vs code\novel_app002\novel_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\project\vs code\novel_app002\novel_app\windows;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\just_audio_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="4.2.9";FLUTTER_VERSION_MAJOR=4;FLUTTER_VERSION_MINOR=2;FLUTTER_VERSION_PATCH=9;FLUTTER_VERSION_BUILD=0;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"4.2.9\";FLUTTER_VERSION_MAJOR=4;FLUTTER_VERSION_MINOR=2;FLUTTER_VERSION_PATCH=9;FLUTTER_VERSION_BUILD=0;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\project\vs code\novel_app002\novel_app\windows;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\just_audio_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\project\vs code\novel_app002\novel_app\windows;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\just_audio_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Release\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\file_selector_windows\Release\file_selector_windows_plugin.lib;..\plugins\just_audio_windows\Release\just_audio_windows_plugin.lib;..\plugins\permission_handler_windows\Release\permission_handler_windows_plugin.lib;..\plugins\share_plus\Release\share_plus_plugin.lib;..\plugins\url_launcher_windows\Release\url_launcher_windows_plugin.lib;..\flutter\Release\flutter_wrapper_app.lib;winmm.lib;D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/project/vs code/novel_app002/novel_app/build/windows/x64/runner/Release/novel_app.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/project/vs code/novel_app002/novel_app/build/windows/x64/runner/Release/novel_app.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\project\vs code\novel_app002\novel_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\project\vs code\novel_app002\novel_app\windows\runner\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/project/vs code/novel_app002/novel_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/project/vs code/novel_app002/novel_app/windows" "-BD:/project/vs code/novel_app002/novel_app/build/windows/x64" --check-stamp-file "D:/project/vs code/novel_app002/novel_app/build/windows/x64/runner/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\project\vs code\novel_app002\novel_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule D:/project/vs code/novel_app002/novel_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/project/vs code/novel_app002/novel_app/windows" "-BD:/project/vs code/novel_app002/novel_app/build/windows/x64" --check-stamp-file "D:/project/vs code/novel_app002/novel_app/build/windows/x64/runner/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\project\vs code\novel_app002\novel_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/project/vs code/novel_app002/novel_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/project/vs code/novel_app002/novel_app/windows" "-BD:/project/vs code/novel_app002/novel_app/build/windows/x64" --check-stamp-file "D:/project/vs code/novel_app002/novel_app/build/windows/x64/runner/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\project\vs code\novel_app002\novel_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\project\vs code\novel_app002\novel_app\windows\runner\flutter_window.cpp" />
    <ClCompile Include="D:\project\vs code\novel_app002\novel_app\windows\runner\main.cpp" />
    <ClCompile Include="D:\project\vs code\novel_app002\novel_app\windows\runner\utils.cpp" />
    <ClCompile Include="D:\project\vs code\novel_app002\novel_app\windows\runner\win32_window.cpp" />
    <ClCompile Include="D:\project\vs code\novel_app002\novel_app\windows\flutter\generated_plugin_registrant.cc" />
    <ResourceCompile Include="D:\project\vs code\novel_app002\novel_app\windows\runner\Runner.rc" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\project\vs code\novel_app002\novel_app\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{9D247693-8023-3B8E-9A5F-7D31506338F8}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\file_selector_windows\file_selector_windows_plugin.vcxproj">
      <Project>{7CFED1C5-8C4C-3032-A787-F5ADF2CC1136}</Project>
      <Name>file_selector_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\project\vs code\novel_app002\novel_app\build\windows\x64\flutter\flutter_assemble.vcxproj">
      <Project>{BBFE3C3B-C0B5-3784-ABC0-C1DA837DA398}</Project>
      <Name>flutter_assemble</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\project\vs code\novel_app002\novel_app\build\windows\x64\flutter\flutter_wrapper_app.vcxproj">
      <Project>{F7AA0C23-44EC-3EB7-9BC1-BBA5662A1260}</Project>
      <Name>flutter_wrapper_app</Name>
    </ProjectReference>
    <ProjectReference Include="D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\just_audio_windows\just_audio_windows_plugin.vcxproj">
      <Project>{CB572C94-B0B7-3C53-AD97-FDE417310752}</Project>
      <Name>just_audio_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\permission_handler_windows\permission_handler_windows_plugin.vcxproj">
      <Project>{48E34391-C67C-300B-8C71-BF6DFE8C4EE8}</Project>
      <Name>permission_handler_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\share_plus\share_plus_plugin.vcxproj">
      <Project>{08762B9F-811C-3841-B945-7D864EFAB405}</Project>
      <Name>share_plus_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\url_launcher_windows\url_launcher_windows_plugin.vcxproj">
      <Project>{EFF88A03-ADFF-3247-8AD7-07D7E096AD1D}</Project>
      <Name>url_launcher_windows_plugin</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>