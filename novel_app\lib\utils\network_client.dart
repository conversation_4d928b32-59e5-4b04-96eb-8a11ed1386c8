import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';
import 'package:flutter/foundation.dart';

/// 网络客户端工具类，确保正确继承系统网络配置
class NetworkClient {
  /// 创建一个能够正确继承系统网络配置的HTTP客户端
  static http.Client createSystemClient({
    Duration? timeout,
    bool enableSystemProxy = true,
  }) {
    if (kIsWeb) {
      // Web平台使用默认客户端
      return http.Client();
    }

    // 创建HttpClient实例
    final httpClient = HttpClient();

    if (enableSystemProxy) {
      // 启用自动重定向
      httpClient.autoUncompress = true;

      // 设置用户代理
      httpClient.userAgent = 'DaiZongAI/1.0 (Windows)';

      // 注意：findProxy方法在某些Flutter版本中可能不可用
      // 系统代理会通过HttpClient自动继承
    }

    // 设置超时
    if (timeout != null) {
      httpClient.connectionTimeout = timeout;
      httpClient.idleTimeout = timeout;
    }

    // 设置安全上下文以支持所有证书
    httpClient.badCertificateCallback = (cert, host, port) {
      // 对于Google API等知名服务，我们信任其证书
      if (host.contains('googleapis.com') ||
          host.contains('google.com') ||
          host.contains('generativelanguage.googleapis.com')) {
        return true;
      }
      return false;
    };

    return IOClient(httpClient);
  }

  /// 创建一个使用指定代理的HTTP客户端
  static http.Client createProxyClient({
    required String proxyHost,
    required int proxyPort,
    Duration? timeout,
  }) {
    if (kIsWeb) {
      throw UnsupportedError('代理客户端在Web平台不受支持');
    }

    final httpClient = HttpClient();

    // 设置代理
    httpClient.findProxy = (uri) => 'PROXY $proxyHost:$proxyPort';

    // 设置超时
    if (timeout != null) {
      httpClient.connectionTimeout = timeout;
      httpClient.idleTimeout = timeout;
    }

    // 启用自动重定向
    httpClient.autoUncompress = true;

    // 设置用户代理
    httpClient.userAgent = 'DaiZongAI/1.0 (Windows; Proxy)';

    return IOClient(httpClient);
  }

  /// 测试网络连接
  static Future<Map<String, dynamic>> testConnection({
    required String url,
    Duration? timeout,
    bool useProxy = false,
    String? proxyHost,
    int? proxyPort,
  }) async {
    http.Client? client;

    try {
      if (useProxy && proxyHost != null && proxyPort != null) {
        client = createProxyClient(
          proxyHost: proxyHost,
          proxyPort: proxyPort,
          timeout: timeout ?? const Duration(seconds: 30),
        );
      } else {
        client = createSystemClient(
          timeout: timeout ?? const Duration(seconds: 30),
          enableSystemProxy: true,
        );
      }

      final startTime = DateTime.now();

      final response = await client.get(
        Uri.parse(url),
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DaiZongAI/1.0 (Connection Test)',
        },
      ).timeout(timeout ?? const Duration(seconds: 30));

      final endTime = DateTime.now();
      final responseTime = endTime.difference(startTime).inMilliseconds;

      return {
        'success': response.statusCode < 500,
        'statusCode': response.statusCode,
        'responseTime': responseTime,
        'message': '连接测试成功，状态码: ${response.statusCode}，响应时间: ${responseTime}ms',
        'headers': response.headers,
        'useProxy': useProxy,
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': '连接测试失败: ${e.toString()}',
        'useProxy': useProxy,
      };
    } finally {
      client?.close();
    }
  }

  /// 检测系统代理配置
  static Future<Map<String, dynamic>> detectSystemProxy() async {
    if (kIsWeb) {
      return {
        'hasProxy': false,
        'message': 'Web平台无法检测系统代理',
      };
    }

    try {
      // 尝试检测系统代理设置
      // 在Windows上，我们可以通过环境变量检测
      final httpProxy = Platform.environment['HTTP_PROXY'] ??
          Platform.environment['http_proxy'];
      final httpsProxy = Platform.environment['HTTPS_PROXY'] ??
          Platform.environment['https_proxy'];

      if (httpProxy != null || httpsProxy != null) {
        return {
          'hasProxy': true,
          'proxyString': httpProxy ?? httpsProxy,
          'message': '检测到环境变量代理: ${httpProxy ?? httpsProxy}',
        };
      } else {
        return {
          'hasProxy': false,
          'message': '未检测到系统代理，使用直连',
        };
      }
    } catch (e) {
      return {
        'hasProxy': false,
        'error': e.toString(),
        'message': '检测系统代理时出错: ${e.toString()}',
      };
    }
  }

  /// 创建用于Google API的专用客户端
  static http.Client createGoogleApiClient({
    Duration? timeout,
    bool useProxy = false,
    String? proxyHost,
    int? proxyPort,
  }) {
    if (useProxy && proxyHost != null && proxyPort != null) {
      return createProxyClient(
        proxyHost: proxyHost,
        proxyPort: proxyPort,
        timeout: timeout ?? const Duration(seconds: 120),
      );
    } else {
      return createSystemClient(
        timeout: timeout ?? const Duration(seconds: 120),
        enableSystemProxy: true,
      );
    }
  }
}
