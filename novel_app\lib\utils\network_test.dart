import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:novel_app/utils/network_client.dart';

/// 网络连接测试工具
class NetworkTest {
  /// 测试Google API连接
  static Future<Map<String, dynamic>> testGoogleApiConnection({
    String? apiKey,
    Duration? timeout,
  }) async {
    const testUrl = 'https://generativelanguage.googleapis.com';

    print('开始测试Google API连接...');
    print('测试URL: $testUrl');

    // 首先检测系统代理
    final proxyInfo = await NetworkClient.detectSystemProxy();
    print('系统代理检测结果: ${proxyInfo['message']}');

    // 测试基本连接（先尝试直连）
    print('尝试直连...');
    var result = await NetworkClient.testConnection(
      url: testUrl,
      timeout: timeout ?? const Duration(seconds: 10),
      useProxy: false,
    );

    // 如果直连失败且检测到可用代理，尝试使用代理
    if (!result['success'] && proxyInfo['workingProxies'] != null && proxyInfo['workingProxies'].isNotEmpty) {
      print('直连失败，尝试使用检测到的代理...');

      for (final proxyUrl in proxyInfo['workingProxies']) {
        print('尝试代理: $proxyUrl');
        final parts = proxyUrl.split(':');
        if (parts.length == 2) {
          final proxyHost = parts[0];
          final proxyPort = int.tryParse(parts[1]);

          if (proxyPort != null) {
            final proxyResult = await NetworkClient.testConnection(
              url: testUrl,
              timeout: timeout ?? const Duration(seconds: 10),
              useProxy: true,
              proxyHost: proxyHost,
              proxyPort: proxyPort,
            );

            if (proxyResult['success']) {
              print('代理连接成功: $proxyUrl');
              result = proxyResult;
              result['usedProxy'] = proxyUrl;
              break;
            }
          }
        }
      }
    }

    print('连接测试结果: ${result['message']}');

    if (result['success'] && apiKey != null && apiKey.isNotEmpty) {
      // 如果基本连接成功且有API密钥，测试实际API调用
      print('开始测试实际API调用...');

      final apiResult = await _testGeminiApiCall(apiKey, timeout);
      return {
        'basicConnection': result,
        'apiCall': apiResult,
        'proxyInfo': proxyInfo,
      };
    }

    return {
      'basicConnection': result,
      'proxyInfo': proxyInfo,
    };
  }

  /// 测试实际的Gemini API调用
  static Future<Map<String, dynamic>> _testGeminiApiCall(
    String apiKey,
    Duration? timeout,
  ) async {
    try {
      final client = NetworkClient.createSystemClient(
        timeout: timeout ?? const Duration(seconds: 30),
      );

      final url =
          'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=$apiKey';

      final requestBody = {
        'contents': [
          {
            'parts': [
              {'text': '请回答"你好"'}
            ]
          }
        ],
        'generationConfig': {
          'maxOutputTokens': 50,
          'temperature': 0.1,
        }
      };

      final response = await client
          .post(
            Uri.parse(url),
            headers: {
              'Content-Type': 'application/json',
            },
            body: jsonEncode(requestBody),
          )
          .timeout(timeout ?? const Duration(seconds: 30));

      client.close();

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        String generatedText = '';

        if (responseData['candidates'] != null &&
            responseData['candidates'].isNotEmpty &&
            responseData['candidates'][0]['content'] != null &&
            responseData['candidates'][0]['content']['parts'] != null &&
            responseData['candidates'][0]['content']['parts'].isNotEmpty) {
          generatedText =
              responseData['candidates'][0]['content']['parts'][0]['text'];
        }

        return {
          'success': true,
          'message': 'API调用成功',
          'statusCode': response.statusCode,
          'generatedText': generatedText,
        };
      } else {
        return {
          'success': false,
          'message': 'API调用失败，状态码: ${response.statusCode}',
          'statusCode': response.statusCode,
          'error': response.body,
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'API调用异常',
        'error': e.toString(),
      };
    }
  }

  /// 测试网络环境
  static Future<Map<String, dynamic>> testNetworkEnvironment() async {
    final results = <String, dynamic>{};

    // 测试基本网络连接
    print('测试基本网络连接...');
    final basicTest = await NetworkClient.testConnection(
      url: 'https://www.google.com',
      timeout: const Duration(seconds: 10),
    );
    results['google'] = basicTest;

    // 测试Google API域名
    print('测试Google API域名...');
    final googleApiTest = await NetworkClient.testConnection(
      url: 'https://generativelanguage.googleapis.com',
      timeout: const Duration(seconds: 10),
    );
    results['googleApi'] = googleApiTest;

    // 检测系统代理
    print('检测系统代理...');
    final proxyInfo = await NetworkClient.detectSystemProxy();
    results['systemProxy'] = proxyInfo;

    // 测试DNS解析
    print('测试DNS解析...');
    try {
      final addresses =
          await InternetAddress.lookup('generativelanguage.googleapis.com');
      results['dnsResolution'] = {
        'success': true,
        'addresses': addresses.map((addr) => addr.address).toList(),
        'message': 'DNS解析成功',
      };
    } catch (e) {
      results['dnsResolution'] = {
        'success': false,
        'error': e.toString(),
        'message': 'DNS解析失败',
      };
    }

    return results;
  }

  /// 打印网络测试报告
  static void printNetworkReport(Map<String, dynamic> results) {
    print('\n========== 网络连接测试报告 ==========');

    if (results.containsKey('google')) {
      final google = results['google'];
      print(
          'Google连接: ${google['success'] ? '✓' : '✗'} - ${google['message']}');
    }

    if (results.containsKey('googleApi')) {
      final googleApi = results['googleApi'];
      print(
          'Google API连接: ${googleApi['success'] ? '✓' : '✗'} - ${googleApi['message']}');
    }

    if (results.containsKey('systemProxy')) {
      final proxy = results['systemProxy'];
      print('系统代理: ${proxy['hasProxy'] ? '已启用' : '未启用'} - ${proxy['message']}');
    }

    if (results.containsKey('dnsResolution')) {
      final dns = results['dnsResolution'];
      print('DNS解析: ${dns['success'] ? '✓' : '✗'} - ${dns['message']}');
      if (dns['success'] && dns['addresses'] != null) {
        print('  解析地址: ${dns['addresses'].join(', ')}');
      }
    }

    if (results.containsKey('basicConnection')) {
      final basic = results['basicConnection'];
      print('基本连接: ${basic['success'] ? '✓' : '✗'} - ${basic['message']}');
    }

    if (results.containsKey('apiCall')) {
      final api = results['apiCall'];
      print('API调用: ${api['success'] ? '✓' : '✗'} - ${api['message']}');
      if (api['success'] && api['generatedText'] != null) {
        print('  生成内容: ${api['generatedText']}');
      }
    }

    print('=====================================\n');
  }
}
